/**
 * Currency formatting utilities for consistent display across the application
 * Formats currency like Excel: $ symbol on left, amount right-aligned, always shows .00
 */

/**
 * Format a number as currency with Excel-like formatting
 * @param value - The numeric value to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  value: number | string | null | undefined,
  options: {
    showSymbol?: boolean;
    minimumFractionDigits?: number;
    maximumFractionDigits?: number;
  } = {}
): string => {
  const {
    showSymbol = true,
    minimumFractionDigits = 2,
    maximumFractionDigits = 2
  } = options;

  // Handle null, undefined, or invalid values
  if (value === null || value === undefined || value === '') {
    return showSymbol ? '$0.00' : '0.00';
  }

  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  // Handle NaN or invalid numbers
  if (isNaN(numValue)) {
    return showSymbol ? '$0.00' : '0.00';
  }

  // Format the number with proper decimal places
  const formattedNumber = numValue.toLocaleString('en-US', {
    minimumFractionDigits,
    maximumFractionDigits
  });

  return showSymbol ? `$${formattedNumber}` : formattedNumber;
};

/**
 * Format currency for table display with Excel-like alignment
 * Returns an object with separate dollar symbol and amount for proper alignment
 * @param value - The numeric value to format
 * @returns Object with symbol and amount for separate rendering
 */
export const formatCurrencyForTable = (
  value: number | string | null | undefined
): { symbol: string; amount: string; fullValue: string } => {
  const formatted = formatCurrency(value);
  
  if (formatted.startsWith('$')) {
    return {
      symbol: '$',
      amount: formatted.substring(1),
      fullValue: formatted
    };
  }
  
  return {
    symbol: '$',
    amount: formatted,
    fullValue: `$${formatted}`
  };
};

/**
 * Format percentage values
 * @param value - The numeric value (e.g., 0.03 for 3%)
 * @param decimalPlaces - Number of decimal places to show
 * @returns Formatted percentage string
 */
export const formatPercentage = (
  value: number | string | null | undefined,
  decimalPlaces: number = 2
): string => {
  if (value === null || value === undefined || value === '') {
    return '0.00%';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return '0.00%';
  }

  // Convert decimal to percentage (e.g., 0.03 -> 3.00%)
  const percentage = numValue * 100;
  
  return `${percentage.toFixed(decimalPlaces)}%`;
};

/**
 * Format large numbers with K, M, B suffixes
 * @param value - The numeric value to format
 * @param decimalPlaces - Number of decimal places to show
 * @returns Formatted string with suffix
 */
export const formatLargeNumber = (
  value: number | string | null | undefined,
  decimalPlaces: number = 1
): string => {
  if (value === null || value === undefined || value === '') {
    return '0';
  }

  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return '0';
  }

  const absValue = Math.abs(numValue);
  
  if (absValue >= 1000000000) {
    return `${(numValue / 1000000000).toFixed(decimalPlaces)}B`;
  } else if (absValue >= 1000000) {
    return `${(numValue / 1000000).toFixed(decimalPlaces)}M`;
  } else if (absValue >= 1000) {
    return `${(numValue / 1000).toFixed(decimalPlaces)}K`;
  }
  
  return numValue.toFixed(decimalPlaces);
};

/**
 * Parse currency string back to number
 * @param currencyString - Currency string like "$1,234.56"
 * @returns Numeric value
 */
export const parseCurrency = (currencyString: string): number => {
  if (!currencyString || typeof currencyString !== 'string') {
    return 0;
  }
  
  // Remove currency symbols, commas, and spaces
  const cleanString = currencyString.replace(/[$,\s]/g, '');
  const numValue = parseFloat(cleanString);
  
  return isNaN(numValue) ? 0 : numValue;
};
