/**
 * Utility function to format dates to MM-DD-YYYY format
 * @param dateString - The date string to format
 * @returns Formatted date string in MM-DD-YYYY format or 'N/A' if invalid
 */
export const formatDateToMMDDYYYY = (dateString: string | null | undefined): string => {
  if (!dateString) return 'N/A';

  try {
    // Handle different date formats that might come from backend
    let date: Date;

    // If it's already in MM-DD-YYYY format, parse it correctly
    if (dateString.includes('-') && dateString.split('-')[0].length <= 2) {
      const [month, day, year] = dateString.split('-');
      date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    }
    // If it's in YYYY-MM-DD format
    else if (dateString.includes('-') && dateString.split('-')[0].length === 4) {
      date = new Date(dateString);
    }
    // If it's in other formats, try to parse directly
    else {
      date = new Date(dateString);
    }

    // Check if date is valid
    if (isNaN(date.getTime())) {
      return dateString; // Return original if can't parse
    }

    // Format to MM-DD-YYYY
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${month}-${day}-${year}`;
  } catch (error) {
    console.warn('Error formatting date:', dateString, error);
    return dateString || 'N/A'; // Return original or N/A if error
  }
};

/**
 * Utility function to format current date to MM-DD-YYYY format
 * @returns Current date in MM-DD-YYYY format
 */
export const getCurrentDateFormatted = (): string => {
  const today = new Date();
  const month = (today.getMonth() + 1).toString().padStart(2, '0');
  const day = today.getDate().toString().padStart(2, '0');
  const year = today.getFullYear();
  
  return `${month}-${day}-${year}`;
}; 